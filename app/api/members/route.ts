import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/lib/auth"
import { db } from "@/lib/db"
import { members, clusters } from "@/lib/db/schema"
import { eq, inArray, and } from "drizzle-orm"
import { getUserAccessibleZones, getUserAccessibleDepartments } from "@/lib/permissions"

export async function GET(request: NextRequest) {
    const session = await auth.api.getSession({
        headers: request.headers
    })

    if (!session) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    try {
        const { searchParams } = new URL(request.url)
        const zoneId = searchParams.get('zoneId')
        const departmentId = searchParams.get('departmentId')

        // Get user's accessible zones and departments
        const accessibleZones = await getUserAccessibleZones((session.user as any).id)
        const accessibleDepartments = await getUserAccessibleDepartments((session.user as any).id)

        // Apply filters based on user permissions
        const conditions = []

        if (zoneId && accessibleZones.includes(parseInt(zoneId))) {
            conditions.push(eq(clusters.zoneId, parseInt(zoneId)))
        } else if (accessibleZones.length > 0) {
            conditions.push(inArray(clusters.zoneId, accessibleZones))
        }

        if (departmentId && accessibleDepartments.includes(parseInt(departmentId))) {
            conditions.push(eq(members.departmentId, parseInt(departmentId)))
        } else if (accessibleDepartments.length > 0) {
            conditions.push(inArray(members.departmentId, accessibleDepartments))
        }

        // Build query with conditional where clause
        const baseQuery = db.select({
            id: members.id,
            firstName: members.firstName,
            lastName: members.lastName,
            email: members.email,
            phone: members.phone,
            memberStatus: members.memberStatus,
            cluster: {
                id: clusters.id,
                name: clusters.clusterName,
                zoneId: clusters.zoneId
            }
        })
            .from(members)
            .leftJoin(clusters, eq(members.clusterId, clusters.id))

        const query = conditions.length > 0
            ? baseQuery.where(and(...conditions))
            : baseQuery

        const result = await query
        return NextResponse.json(result)

    } catch (error) {
        console.error("Error fetching members:", error)
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        )
    }
}

export async function POST(request: NextRequest) {
    const session = await auth.api.getSession({
        headers: request.headers
    })

    if (!session) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    try {
        const body = await request.json()
        const { firstName, lastName, email, phone, clusterId, departmentId } = body

        // Verify user has write access to the specified cluster/department
        // Implementation depends on your specific business rules

        const newMember = await db.insert(members).values({
            firstName,
            lastName,
            email,
            phone,
            clusterId,
            departmentId,
            memberStatus: 'active',
            joinDate: new Date(),
        }).$returningId()

        return NextResponse.json(newMember[0], { status: 201 })

    } catch (error) {
        console.error("Error creating member:", error)
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        )
    }
}